package server

import (
	"fmt"
	"github.com/devtron-labs/common-lib/utils"
	"github.com/devtron-labs/scoop/internal/api"
	"github.com/devtron-labs/scoop/internal/config"
	"github.com/devtron-labs/scoop/internal/middleware"
	"github.com/devtron-labs/scoop/pkg/clusterCache/cache"
	cluster "github.com/devtron-labs/scoop/pkg/clusterResources"
	"github.com/devtron-labs/scoop/pkg/kubectl"
	"github.com/devtron-labs/scoop/pkg/namespaces"
	"github.com/devtron-labs/scoop/types"
	"log"
	"net/http"
)

// Server represents the HTTP server
type Server struct {
	Config *config.AppConfig
	Stop   <-chan struct{}
}

// NewServer creates a new server instance
func NewServer(cfg *config.AppConfig, stop <-chan struct{}) *Server {
	return &Server{
		Config: cfg,
		Stop:   stop,
	}
}

// Run starts the server
func (s *Server) Run() error {
	errChan := make(chan error)

	// Register event handlers
	kubectl.RegisterEventHandler(kubectl.PodHandler.Kind, kubectl.PodHandler)
	kubectl.RegisterEventHandler(kubectl.NodeHandler.Kind, kubectl.NodeHandler)

	// Initialize the watcher
	watcher := kubectl.NewKubeWatch(s.Config.KubeConfig, s.Config.ResyncDuration, s.Config.Retention, errChan)
	watcher.AddWatch()

	// Initialize logger
	logger, err := utils.NewSugardLogger()
	if err != nil {
		return err
	}

	// Initialize handlers
	onResourceUpdateHandlers := make([]cache.OnResourceUpdatedHandler, 0)
	watcherEnvConfig := types.NewWatcherConfig()

	// Initialize API handlers
	podHandler := api.NewPodHandler(s.Config.PassKey)
	nodeHandler := api.NewNodeHandler(s.Config.PassKey)
	pprofHandler := api.NewPprofHandler()

	// Setup routes
	http.HandleFunc(config.PodInfoEndpoint, middleware.AuthMiddleware(s.Config.PassKey, podHandler.GetPodInfo))
	http.HandleFunc(config.PodListEndpoint, middleware.AuthMiddleware(s.Config.PassKey, podHandler.GetPodList))
	http.HandleFunc(config.NodeListEndpoint, middleware.AuthMiddleware(s.Config.PassKey, nodeHandler.GetNodeList))

	// Register pprof routes
	for path, handler := range pprofHandler.RegisterRoutes() {
		http.HandleFunc(path, handler)
	}

	// Setup watcher if configured
	if watcherEnvConfig.IsValid() {
		namespaceStore := namespaces.NewNamespaceStore()

		// Import the watcher package dynamically to avoid import cycles
		watcherHandler := setupWatcher(s.Config.PassKey, namespaceStore, onResourceUpdateHandlers)

		http.HandleFunc(config.WatcherCUDEndpoint, middleware.AuthMiddleware(s.Config.PassKey, watcherHandler.HandleWatchCUD))
		http.HandleFunc(config.WatcherGetEndpoint, middleware.AuthMiddleware(s.Config.PassKey, watcherHandler.GetWatcher))
		http.HandleFunc(config.NamespaceCUDEndpoint, middleware.AuthMiddleware(s.Config.PassKey, watcherHandler.HandleNamespaceCUD))
	}

	// Initialize resource manager
	resourceManager, err := cluster.NewClusterResourceManagerImpl(s.Config.KubeConfig, onResourceUpdateHandlers)
	if err != nil {
		logger.Fatalw("failed to initialise cluster resource manager", "err", err)
	}

	resourceHandler := api.NewResourceHandler(logger, resourceManager)

	http.HandleFunc(config.ApiResourcesEndpoint, middleware.AuthMiddleware(s.Config.PassKey, resourceHandler.GetApiResource))
	http.HandleFunc(config.ResourceListEndpoint, middleware.AuthMiddleware(s.Config.PassKey, resourceHandler.GetResourceList))
	http.HandleFunc(config.ResourceTreeEndpoint, middleware.AuthMiddleware(s.Config.PassKey, resourceHandler.GetResourceTreeForNodes))
	http.HandleFunc(config.K8sCacheConfigEndpoint, middleware.AuthMiddleware(s.Config.PassKey, resourceHandler.UpdateResourceCacheConfig))

	// Start the server
	go func() {
		fmt.Println("starting server on", s.Config.Port)
		err := http.ListenAndServe(s.Config.Port, nil)
		log.Fatal(err)
	}()

	// Wait for shutdown signal
	select {
	case <-s.Stop:
		// We are done
		watcher.Stop()
		// Stop the event watcher if it was started
		stopEventWatcher()
		return nil
	case err := <-errChan:
		// Error starting or running a runnable
		return err
	}
}
