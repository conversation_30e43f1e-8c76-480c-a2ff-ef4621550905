package server

import (
	"github.com/devtron-labs/scoop/internal/api"
	"github.com/devtron-labs/scoop/pkg/clusterCache/cache"
	"github.com/devtron-labs/scoop/pkg/namespaces"
	"github.com/devtron-labs/scoop/pkg/watcher"
	"github.com/devtron-labs/scoop/types"
)

// Global variable to store the event watcher service for stopping it later
var eventWatcherServiceInstance *watcher.EventWatcherService

// setupWatcher initializes the watcher service and returns a handler
func setupWatcher(passKey string, namespaceStore namespaces.NamespaceStore, handlers []cache.OnResourceUpdatedHandler) *api.WatcherHandler {
	// Get the watcher config
	watcherEnvConfig := types.NewWatcherConfig()

	// Create the event watcher service
	eventWatcherServiceInstance = watcher.NewEventWatcher(watcherEnvConfig, namespaceStore)

	// Add the event watcher service to the resource update handlers
	handlers = append(handlers, eventWatcherServiceInstance.OnResourceUpdate)

	// Start watching for events
	go eventWatcherServiceInstance.Watch()

	// Create and return the watcher handler
	return api.NewWatcherHandler(passKey, namespaceStore, eventWatcherServiceInstance)
}

// stopEventWatcher stops the event watcher service if it was started
func stopEventWatcher() {
	if eventWatcherServiceInstance != nil {
		eventWatcherServiceInstance.Stop()
		eventWatcherServiceInstance = nil
	}
}
