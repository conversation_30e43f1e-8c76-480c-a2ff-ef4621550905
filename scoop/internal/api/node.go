package api

import (
	"fmt"
	"github.com/devtron-labs/scoop/internal/middleware"
	"github.com/devtron-labs/scoop/pkg/kubectl"
	"net/http"
	"strings"
)

// NodeHandler handles node-related API requests
type NodeHandler struct {
	PassKey string
}

// NewNodeHandler creates a new node handler
func NewNodeHandler(passKey string) *NodeHandler {
	return &NodeHandler{
		PassKey: passKey,
	}
}

// GetNodeList handles requests for listing nodes
func (h *NodeHandler) GetNodeList(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		namesOnly := r.URL.Query().Get("names-only")
		if len(namesOnly) != 0 {
			var nodeNames []string
			nodes := kubectl.GetNodeList()
			for _, node := range nodes {
				nodeNames = append(nodeNames, fmt.Sprintf("%s", node.Name))
			}
			formattedNodeNames := strings.Join(nodeNames, "\n")
			fmt.Fprint(w, formattedNodeNames)
		} else {
			middleware.WriteResponse(w, http.StatusBadRequest, nil, "names-only parameter is required")
		}
	default:
		middleware.WriteResponse(w, http.StatusBadRequest, nil, fmt.Sprintf("Unsupported method: %s", r.Method))
	}
}
