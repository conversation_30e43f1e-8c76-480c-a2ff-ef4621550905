package api

import (
	"fmt"
	"github.com/devtron-labs/scoop/internal/config"
	"github.com/devtron-labs/scoop/internal/middleware"
	"github.com/devtron-labs/scoop/pkg/kubectl"
	"net/http"
	"strings"
)

// PodHandler handles pod-related API requests
type Pod<PERSON><PERSON>ler struct {
	PassKey string
}

// NewPodHandler creates a new pod handler
func NewPodHandler(passKey string) *PodHandler {
	return &PodHandler{
		PassKey: passKey,
	}
}

// GetPodInfo handles requests for individual pod information
func (h *PodHandler) GetPodInfo(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		podName := r.URL.Query().Get("name")
		namespace := r.URL.Query().Get("namespace")
		if len(podName) == 0 {
			middleware.WriteResponse(w, http.StatusBadRequest, nil, "podname is mandatory")
			return
		}
		if len(namespace) == 0 {
			namespace = config.DefaultNamespace
		}
		if podInfo := kubectl.GetPodInfo(fmt.Sprintf("%s/%s", namespace, podName)); podInfo != nil {
			if formattedPodInfo, err := podInfo.DetailTabbedFormatter(); err == nil {
				middleware.WriteResponse(w, http.StatusOK, formattedPodInfo, "")
				return
			}
		}
		middleware.WriteResponse(w, http.StatusNoContent, nil, "unable to retrieve data")

	case http.MethodPost:
		middleware.WriteResponse(w, http.StatusBadRequest, nil, "POST not supported")
	default:
		middleware.WriteResponse(w, http.StatusBadRequest, nil, fmt.Sprintf("Unsupported method: %s", r.Method))
	}
}

// GetPodList handles requests for listing pods
func (h *PodHandler) GetPodList(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		namesOnly := r.URL.Query().Get("names-only")
		appId := r.URL.Query().Get("appId")
		envId := r.URL.Query().Get("envId")
		var keys []string
		if len(r.URL.Query().Get("keys")) > 0 {
			keys = strings.Split(strings.TrimSpace(r.URL.Query().Get("keys")), ",")
		}
		podFilter := kubectl.PodFilter{EnvId: envId, AppId: appId}
		pods := kubectl.GetPodList(podFilter)
		if len(namesOnly) != 0 {
			var podNames []string
			for _, pod := range pods {
				podNames = append(podNames, fmt.Sprintf("%s/%s", pod.Namespace, pod.Name))
			}
			formattedPodNames := strings.Join(podNames, "\n")
			fmt.Fprint(w, formattedPodNames)
		} else if len(keys) > 0 {
			for _, podInfo := range pods {
				fmt.Fprint(w, fmt.Sprintf("%s,%s,%s\n", podInfo.AppId, podInfo.EnvId, podInfo.Name))
			}
		} else {
			for _, podInfo := range pods {
				if formattedPodInfo, err := podInfo.DetailTabbedFormatter(); err == nil {
					fmt.Fprint(w, formattedPodInfo)
					fmt.Fprint(w, "\n---\n")
				}
			}
		}
	default:
		middleware.WriteResponse(w, http.StatusBadRequest, nil, fmt.Sprintf("Unsupported method: %s", r.Method))
	}
}
